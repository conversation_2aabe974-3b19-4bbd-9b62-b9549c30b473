##HIC Income Categories

import os
import glob
import pandas as pd
import re
import warnings
from datetime import datetime

# Import rich_progress for gradient progress bars
import rich_progress

from pandas.core.common import SettingWithCopyWarning
warnings.simplefilter(action="ignore", category=SettingWithCopyWarning)

# Helper functions for rich progress bars
def print_header(title):
    """Print a colorful header with the given title."""
    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    rich_progress.print_status(f"\n{title}", "header")
    rich_progress.print_status(timestamp, "info")
    rich_progress.print_status("=" * 50, "info")

def print_section(title):
    """Print a section header."""
    rich_progress.print_status(f"\n>> {title}", "info")
    rich_progress.print_status("-" * (len(title) + 4), "info")

# Print welcome header
print_header("HIC Email Domain Filtering Tool - World Bank Income Categories")

# Get the path from the user
print_section("Input Path")
path = input("Loc: ")
os.chdir(path)
rich_progress.print_status(f"Working directory: {os.getcwd()}", "info")

# Extract conference segment name
print_section("Conference Segment Detection")

# Define a regex pattern to match the desired segment, including spaces
pattern = r"\\([\w\s-]+\d{4})\\?"

# Search for the pattern in the path
match = re.search(pattern, path)

if match:
    csn = match.group(1)
    rich_progress.print_status(f"Found segment: {csn}", "success")
else:
    rich_progress.print_status(f"Desired segment not found in path: {path}", "warning")
    # Prompt for manual input
    csn = input("Please enter the conference segment name (e.g., 'Conference 2023'): ")
    if csn.strip():
        rich_progress.print_status(f"Using manually entered segment: {csn}", "info")
    else:
        rich_progress.print_status("No segment name provided. Exiting.", "error")
        exit()

# Find all CSV files in the directory
print_section("Finding CSV Files")
csv_files = glob.glob('*.csv')
if not csv_files:
    rich_progress.print_status("No CSV files found in the directory.", "error")
    exit()
rich_progress.print_status(f"Found {len(csv_files)} CSV files", "success")

# Read and concatenate CSV files with progress bar
print_section("Reading CSV Files")
rich_progress.print_status(f"Reading {len(csv_files)} CSV files...", "info")

# Create a progress bar for reading CSV files
read_bar, update_read = rich_progress.create_progress_bar(
    total=len(csv_files),
    description="Reading CSV files",
    color_scheme="blue"
)

# Read each CSV file with progress tracking
dfs = []
for csv_file in csv_files:
    try:
        df = pd.read_csv(csv_file)
        dfs.append(df)
        update_read(1, f"Read {csv_file}")
    except Exception as e:
        rich_progress.print_status(f"Error reading {csv_file}: {str(e)}", "error")
        update_read(1, f"Error with {csv_file}")

# Stop the progress bar
read_bar.stop()

# Concatenate all dataframes
rich_progress.print_status("Concatenating files...", "info")
d2_EVENT = pd.concat(dfs, ignore_index=True)
rich_progress.print_status(f"Successfully read {len(d2_EVENT)} records from {len(csv_files)} files", "success")

# Define World Bank income categories (as of 2024-2025)
print_section("Defining World Bank Income Categories")

# High-income economies ($14,006 or more)
high_income_tlds = [
    '.ae', '.ag', '.ai', '.an', '.aq', '.ar', '.at', '.au', '.aw', '.bb', '.be', '.bh', '.bm', '.bn', '.bs', 
    '.ca', '.ch', '.cl', '.cy', '.cz', '.de', '.dk', '.ee', '.es', '.fi', '.fr', '.gb', '.gd', '.gi', '.gl', 
    '.gr', '.hk', '.hr', '.hu', '.ie', '.il', '.im', '.is', '.it', '.je', '.jp', '.kn', '.kr', '.kw', '.ky', 
    '.li', '.lt', '.lu', '.lv', '.mc', '.mt', '.mu', '.mx', '.my', '.nl', '.no', '.nz', '.om', '.pa', '.pl', 
    '.pr', '.pt', '.qa', '.ro', '.ru', '.sa', '.se', '.sg', '.si', '.sk', '.sm', '.tc', '.tt', '.tw', '.uk', 
    '.us', '.uy', '.va', '.vg', '.vi'
]

# Upper-middle-income economies ($4,516 to $14,005)
upper_middle_income_tlds = [
    '.al', '.am', '.az', '.ba', '.bg', '.bz', '.cn', '.co', '.cr', '.cu', '.do', '.dz', '.ec', '.fj', '.ga', 
    '.ge', '.gq', '.gt', '.gy', '.id', '.ir', '.jm', '.jo', '.kz', '.lb', '.ly', '.md', '.me', '.mk', '.mn', 
    '.mv', '.pe', '.py', '.rs', '.sr', '.th', '.tj', '.tm', '.tr', '.ua', '.ve', '.za'
]

# Lower-middle-income economies ($1,146 to $4,515)
lower_middle_income_tlds = [
    '.ao', '.bd', '.bj', '.bo', '.bt', '.ci', '.cm', '.cv', '.dj', '.eg', '.fm', '.gh', '.gm', '.gn', '.hn', 
    '.ht', '.in', '.ke', '.kg', '.kh', '.ki', '.km', '.la', '.lk', '.ls', '.ma', '.mh', '.mm', '.mr', '.na', 
    '.ng', '.ni', '.np', '.nr', '.pk', '.pg', '.ph', '.ps', '.pw', '.sb', '.sn', '.so', '.st', '.sv', '.sz', 
    '.tl', '.tn', '.tv', '.tz', '.ug', '.uz', '.vu', '.ws', '.zm', '.zw'
]

# Low-income economies ($1,145 or less)
low_income_tlds = [
    '.af', '.bi', '.bf', '.cd', '.cf', '.er', '.et', '.gw', '.kp', '.lr', '.mg', '.ml', '.mw', '.mz', '.ne', 
    '.rw', '.sd', '.sl', '.ss', '.sy', '.td', '.tg', '.ye'
]

# Special domains (not country-specific but important)
special_domains = [
    '.edu', '.gov', '.mil', '.org', '.int'
]

# Chinese domains (common Chinese email providers)
chinese_domains = [
    '163.com', 'qq.com', '126.com', 'sina.com', 'sohu.com', 'tom.com', 'aliyun.com', 
    '21cn.com', 'baidu.com', 'yeah.net', 'sogou.com', '163.net', 'sina.net', 'chinaren.com'
]

# Create filters for each income category
print_section("Filtering by Income Categories")

# Create a progress bar for filtering
filter_bar, update_filter = rich_progress.create_progress_bar(
    total=4,  # 4 income categories
    description="Filtering by income categories",
    color_scheme="green"
)

# High-income filter
high_income_filter = pd.Series(False, index=d2_EVENT.index)
for tld in high_income_tlds:
    high_income_filter = high_income_filter | d2_EVENT["Email"].str.endswith(tld, na=False)
df_high_income = d2_EVENT[high_income_filter]
update_filter(1, f"Filtered high-income countries ({len(df_high_income)} records)")

# Upper-middle-income filter
upper_middle_filter = pd.Series(False, index=d2_EVENT.index)
for tld in upper_middle_income_tlds:
    upper_middle_filter = upper_middle_filter | d2_EVENT["Email"].str.endswith(tld, na=False)
df_upper_middle = d2_EVENT[upper_middle_filter]
update_filter(1, f"Filtered upper-middle-income countries ({len(df_upper_middle)} records)")

# Lower-middle-income filter
lower_middle_filter = pd.Series(False, index=d2_EVENT.index)
for tld in lower_middle_income_tlds:
    lower_middle_filter = lower_middle_filter | d2_EVENT["Email"].str.endswith(tld, na=False)
df_lower_middle = d2_EVENT[lower_middle_filter]
update_filter(1, f"Filtered lower-middle-income countries ({len(df_lower_middle)} records)")

# Low-income filter
low_income_filter = pd.Series(False, index=d2_EVENT.index)
for tld in low_income_tlds:
    low_income_filter = low_income_filter | d2_EVENT["Email"].str.endswith(tld, na=False)
df_low_income = d2_EVENT[low_income_filter]
update_filter(1, f"Filtered low-income countries ({len(df_low_income)} records)")

# Stop the progress bar
filter_bar.stop()

# Special filters
print_section("Creating Special Filters")

# Special domains filter
special_filter = pd.Series(False, index=d2_EVENT.index)
for domain in special_domains:
    special_filter = special_filter | d2_EVENT["Email"].str.endswith(domain, na=False)
df_special = d2_EVENT[special_filter]
rich_progress.print_status(f"Special domains: {len(df_special)} records", "info")

# Chinese domains filter
chinese_filter = pd.Series(False, index=d2_EVENT.index)
for domain in chinese_domains:
    chinese_filter = chinese_filter | d2_EVENT["Email"].str.endswith(domain, na=False)
chinese_filter = chinese_filter | d2_EVENT["Email"].str.endswith(".cn", na=False)
df_chinese = d2_EVENT[chinese_filter]
rich_progress.print_status(f"Chinese domains: {len(df_chinese)} records", "info")

# Education domains filter
education_filter = d2_EVENT["Email"].str.contains(".edu", na=False)
df_education = d2_EVENT[education_filter]
rich_progress.print_status(f"Education domains: {len(df_education)} records", "info")

# Print counts with consistent formatting
print_section("Filtering Results")
rich_progress.print_status("Domain filtering complete. Results:", "success")
rich_progress.print_status("-" * 40, "info")
rich_progress.print_status(f"{'Category':<25} {'Count':>10}", "info")
rich_progress.print_status("-" * 40, "info")

# Print counts for income categories
rich_progress.print_status(f"{'High Income':<25} {len(df_high_income):>10}", "info")
rich_progress.print_status(f"{'Upper-Middle Income':<25} {len(df_upper_middle):>10}", "info")
rich_progress.print_status(f"{'Lower-Middle Income':<25} {len(df_lower_middle):>10}", "info")
rich_progress.print_status(f"{'Low Income':<25} {len(df_low_income):>10}", "info")

# Print counts for special categories
rich_progress.print_status(f"{'Special Domains':<25} {len(df_special):>10}", "info")
rich_progress.print_status(f"{'Chinese Domains':<25} {len(df_chinese):>10}", "info")
rich_progress.print_status(f"{'Education Domains':<25} {len(df_education):>10}", "info")
rich_progress.print_status("-" * 40, "info")

# Create output directories
print_section("Creating Output Directories")
income_dir = os.path.join(os.getcwd(), "income_categories")
special_dir = os.path.join(os.getcwd(), "special_categories")

# Create all directories
os.makedirs(income_dir, exist_ok=True)
os.makedirs(special_dir, exist_ok=True)

rich_progress.print_status(f"Created income categories directory: {income_dir}", "info")
rich_progress.print_status(f"Created special categories directory: {special_dir}", "info")

print_section("Organizing Output Files")

# Define all dataframes to save with their filenames and target directories
# Format: (dataframe, filename, description, directory)
files_to_save = [
    # Income category files
    (df_high_income, f"{csn}_High_Income.csv", "High Income", income_dir),
    (df_upper_middle, f"{csn}_Upper_Middle_Income.csv", "Upper-Middle Income", income_dir),
    (df_lower_middle, f"{csn}_Lower_Middle_Income.csv", "Lower-Middle Income", income_dir),
    (df_low_income, f"{csn}_Low_Income.csv", "Low Income", income_dir),
    
    # Special category files
    (df_special, f"{csn}_Special_Domains.csv", "Special Domains", special_dir),
    (df_chinese, f"{csn}_Chinese_Domains.csv", "Chinese Domains", special_dir),
    (df_education, f"{csn}_Education_Domains.csv", "Education Domains", special_dir)
]

# Create a progress bar for saving files
save_bar, update_save = rich_progress.create_progress_bar(
    total=len(files_to_save),
    description="Saving files",
    color_scheme="green"
)

# Save each file with progress tracking
for df, filename, category, directory in files_to_save:
    try:
        output_path = os.path.join(directory, filename)
        df.to_csv(output_path, encoding='utf-8-sig', index=False)
        update_save(1, f"Saved {category} ({len(df)} records) to {os.path.basename(directory)}")
    except Exception as e:
        rich_progress.print_status(f"Error saving {filename}: {str(e)}", "error")
        update_save(1, f"Error with {filename}")

# Stop the progress bar
save_bar.stop()

# Print completion message
print_header("Processing Completed Successfully!")
rich_progress.print_status(f"Conference segment: {csn}", "success")
rich_progress.print_status(f"Total records processed: {len(d2_EVENT)}", "success")

# Count files by directory
income_files = sum(1 for _, _, _, dir in files_to_save if dir == income_dir)
special_files = sum(1 for _, _, _, dir in files_to_save if dir == special_dir)

rich_progress.print_status(f"Total output files created: {len(files_to_save)}", "success")
rich_progress.print_status(f"Files in income categories folder: {income_files}", "info")
rich_progress.print_status(f"Files in special categories folder: {special_files}", "info")
rich_progress.print_status(f"Output directories: {income_dir}, {special_dir}", "info")
